/* Tailwind CSS 自定义补充样式 */

/* 自定义 min-height 类 */
.min-h-250 {
    min-height: 250px;
}

.min-h-300 {
    min-height: 300px;
}

/* 响应式 min-height 类 */
@media (min-width: 768px) {
    .md\:min-h-250 {
        min-height: 250px;
    }

    .md\:min-h-300 {
        min-height: 300px;
    }
}

/* 基础重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    overflow-y: scroll; /* 始终显示垂直滚动条，避免页面布局跳动 */
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Roboto, "Helvetica Neue", Arial, sans-serif;
    line-height: 1.6;
    background: #f8fafc;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
    font-size: 16px;
}

/* 模态框动画 */
@keyframes fade-in {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* 滑入动画 */
@keyframes slide-in {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 模态框滑入动画 */
@keyframes modal-slide-in {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* 动画类 */
.animate-fade-in {
    animation: fade-in 0.3s ease;
}

.animate-slide-in {
    animation: slide-in 0.6s ease;
}

.animate-modal-slide-in {
    animation: modal-slide-in 0.3s ease;
}

.animate-fade-in {
    animation: fade-in 0.3s ease;
}

.animate-slide-in {
    animation: slide-in 0.3s ease;
}

/* 模态框显示状态 */
.modal {
    display: none;
}

.modal.show {
    display: block !important;
}

/* 滚动条美化 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* 高亮搜索结果 */
.highlight {
    background: linear-gradient(135deg, rgba(240, 147, 251, 0.3) 0%, rgba(102, 126, 234, 0.3) 100%);
    padding: 2px 4px;
    border-radius: 4px;
    font-weight: 600;
}

/* 加载动画 */
@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* 标签组件样式 */
.tag {
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 500;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 9999px;
    transition: all 0.3s ease;
    white-space: nowrap;
    text-decoration: none;
}

.tag:hover {
    transform: translateY(-1px) scale(1.05);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* 标签移除按钮样式 */
.tag .remove-tag {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    margin-left: 0.25rem;
    font-size: 12px;
    font-weight: bold;
    cursor: pointer;
    border-radius: 50%;
    transition: all 0.2s ease;
    background: rgba(255, 255, 255, 0.2);
}

.tag .remove-tag:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* 确保标签容器的样式 */
#tagsContainer,
#editTagsContainer {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

/* 标签输入框焦点状态 */
.tags-input-wrapper:focus-within {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 文本截断工具类 */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .modal .bg-white {
        margin: 2% auto;
        width: 95%;
        max-height: 95vh;
    }

    /* 内容区域内边距优化 - 减少移动端的内边距 */
    .modal .p-8 {
        padding: 1rem;
    }

    /* 头部和底部区域内边距优化 */
    .modal .px-8 {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    /* 确保编辑器在移动端有合适的高度 */
    #editModal .min-h-250 {
        min-height: 180px;
    }

    /* 移动端工具栏优化 */
    #editModal .markdown-toolbar {
        padding: 6px 8px;
        gap: 2px;
    }

    #editModal .toolbar-btn {
        width: 28px;
        height: 28px;
        font-size: 12px;
    }

    /* 标签区域内边距优化 */
    #editModal .group {
        padding: 8px 12px !important;
        min-height: 48px !important;
    }

    /* 标签容器间距优化 */
    #editModal .space-y-6 > * + * {
        margin-top: 1rem;
    }

    /* 标签输入框内边距优化 */
    #editModal .group input {
        padding: 4px 6px !important;
    }

    /* 标签标题间距优化 */
    #editModal .mb-1\.5 {
        margin-bottom: 0.5rem !important;
    }

    /* 标签容器内部间距优化 */
    #editModal .gap-2 {
        gap: 0.375rem !important;
    }
}

@media (max-width: 480px) {
    .modal .bg-white {
        margin: 1% auto;
        width: 98%;
        max-height: 98vh;
    }

    /* 小屏幕设备内容区域内边距进一步减少 */
    .modal .p-8 {
        padding: 0.75rem;
    }

    /* 头部和底部区域内边距进一步减少 */
    .modal .px-8 {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    /* 小屏幕设备进一步优化 */
    #editModal .min-h-250 {
        min-height: 150px;
    }

    /* 标签输入区域进一步优化 */
    #editModal .group {
        padding: 6px 10px !important;
        min-height: 44px !important;
    }

    /* 标签容器间距进一步优化 */
    #editModal .space-y-6 > * + * {
        margin-top: 0.75rem;
    }

    /* 标签输入框内边距进一步优化 */
    #editModal .group input {
        padding: 3px 4px !important;
        min-width: 100px !important;
    }

    /* 标签标题间距进一步优化 */
    #editModal .mb-1\.5 {
        margin-bottom: 0.375rem !important;
    }

    /* 标签容器内部间距进一步优化 */
    #editModal .gap-2 {
        gap: 0.25rem !important;
    }

    /* 标签元素间距优化 */
    #editModal .gap-4 {
        gap: 0.5rem !important;
    }

    /* 编辑器内边距优化 */
    #editModal textarea {
        padding: 1rem !important;
    }

    /* 保存按钮优化 */
    #editModal button[type="submit"] {
        padding: 12px 20px;
        font-size: 16px;
        min-height: 44px;
    }

    /* 关闭按钮优化 */
    #editModal button[data-dismiss="modal"] {
        width: 44px;
        height: 44px;
        font-size: 24px;
    }

    /* Now按钮优化 */
    #editModal #setCurrentTimeBtn {
        min-height: 36px;
        padding: 8px 12px;
        font-size: 14px;
    }

    /* 时间输入框优化 */
    #editModal #editPostTime {
        min-height: 36px;
        padding: 8px 12px;
        font-size: 14px;
    }

    /* 头部区域内边距优化 */
    #editModal .py-6 {
        padding-top: 1rem !important;
        padding-bottom: 1rem !important;
    }
}

/* 触摸设备特定优化 */
@media (hover: none) and (pointer: coarse) {
    /* 移除hover效果，避免在触摸设备上的粘滞问题 */
    #editModal .toolbar-btn:hover {
        background: transparent;
        color: #64748b;
    }

    /* 增加触摸目标大小 */
    #editModal .toolbar-btn {
        min-width: 44px;
        min-height: 44px;
    }

    /* 优化按钮间距 */
    #editModal .markdown-toolbar {
        gap: 8px;
    }
}

/* 移动端滚动优化 */
@media (max-width: 768px) {
    /* 平滑滚动 */
    #editModal .overflow-y-auto {
        -webkit-overflow-scrolling: touch;
        scroll-behavior: smooth;
    }

    /* 防止内容被虚拟键盘遮挡 */
    #editModal {
        padding-bottom: env(keyboard-inset-height, 0px);
    }
}

/* 防止缩放和改善触摸体验 */
@media (max-width: 768px) {
    #editModal input,
    #editModal textarea {
        font-size: 16px; /* 防止iOS自动缩放 */
        -webkit-appearance: none;
        border-radius: 8px;
    }

    /* 改善文本选择体验 */
    #editModal textarea {
        -webkit-user-select: text;
        user-select: text;
    }
}
