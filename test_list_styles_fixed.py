#!/usr/bin/env python3
"""
测试列表样式修复的验证脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from markdown_utils import render_markdown

def test_list_rendering():
    """测试列表渲染"""
    print("🧪 测试修复后的列表渲染...")
    
    test_cases = [
        {
            'name': '有序列表',
            'input': '1. 第一项\n2. 第二项\n3. 第三项',
            'expected_tag': 'ol',
            'expected_style': 'decimal'
        },
        {
            'name': '无序列表',
            'input': '- 第一项\n- 第二项\n- 第三项',
            'expected_tag': 'ul',
            'expected_style': 'disc'
        },
        {
            'name': '混合内容',
            'input': '# 标题\n\n1. 有序项\n2. 有序项\n\n段落文本\n\n- 无序项\n- 无序项',
            'expected_tags': ['h1', 'ol', 'p', 'ul']
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 测试 {i}: {test_case['name']}")
        print(f"输入: {repr(test_case['input'])}")
        
        try:
            result = render_markdown(test_case['input'])
            print(f"输出: {result}")
            
            # 检查HTML标签
            if 'expected_tag' in test_case:
                tag = test_case['expected_tag']
                if f'<{tag}>' in result:
                    print(f"✅ 包含正确的标签: <{tag}>")
                else:
                    print(f"❌ 缺少标签: <{tag}>")
            
            if 'expected_tags' in test_case:
                for tag in test_case['expected_tags']:
                    if f'<{tag}>' in result or f'<{tag} ' in result:
                        print(f"✅ 包含标签: <{tag}>")
                    else:
                        print(f"❌ 缺少标签: <{tag}>")
                        
        except Exception as e:
            print(f"❌ 测试异常: {e}")

def test_css_styles():
    """测试CSS样式修复"""
    print("\n🎨 测试CSS样式修复...")
    
    # 检查模板文件中的CSS修复
    templates = ['templates/index.html', 'templates/random.html']
    
    for template in templates:
        print(f"\n📄 检查 {template}:")
        try:
            with open(template, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否包含修复的CSS
            if '.markdown-content ol {' in content and 'list-style-type: decimal;' in content:
                print("✅ 有序列表样式已修复")
            else:
                print("❌ 有序列表样式未修复")
            
            if '.markdown-content ul {' in content and 'list-style-type: disc;' in content:
                print("✅ 无序列表样式已修复")
            else:
                print("❌ 无序列表样式未修复")
                
        except Exception as e:
            print(f"❌ 检查模板失败: {e}")

def test_visual_verification():
    """视觉验证指南"""
    print("\n👁️ 视觉验证指南:")
    print("请在浏览器中验证以下内容：")
    print()
    print("1. 主页 (http://localhost:5001):")
    print("   - 查看包含有序列表的微博")
    print("   - 确认每个列表项前面显示数字序号 (1. 2. 3.)")
    print("   - 确认无序列表显示圆点符号")
    print()
    print("2. 随机微博页面 (http://localhost:5001/random):")
    print("   - 随机浏览包含列表的微博")
    print("   - 确认列表样式正确显示")
    print()
    print("3. 编辑功能:")
    print("   - 编辑包含列表的微博")
    print("   - 确认编辑后列表样式保持正确")
    print()
    print("4. 新发布微博:")
    print("   - 发布包含有序列表的新微博")
    print("   - 确认立即显示正确的序号")

def test_browser_compatibility():
    """浏览器兼容性说明"""
    print("\n🌐 浏览器兼容性:")
    print("修复的CSS样式应该在以下浏览器中正常工作：")
    print("✅ Chrome/Chromium")
    print("✅ Firefox")
    print("✅ Safari")
    print("✅ Edge")
    print()
    print("使用的CSS属性：")
    print("- list-style-type: decimal (有序列表)")
    print("- list-style-type: disc (无序列表)")
    print("- padding-left: 24px (缩进)")
    print("- margin: 8px 0 (间距)")

def main():
    """主测试函数"""
    print("🚀 开始测试列表样式修复...")
    print("=" * 60)
    
    # 运行所有测试
    test_list_rendering()
    test_css_styles()
    test_visual_verification()
    test_browser_compatibility()
    
    print("\n" + "=" * 60)
    print("📋 修复总结:")
    print()
    print("✅ 问题诊断: Tailwind CSS重置样式设置了 list-style: none")
    print("✅ 解决方案: 在 .markdown-content 中重新设置 list-style-type")
    print("✅ 修复范围: 主页面和随机微博页面")
    print("✅ 兼容性: 所有现代浏览器")
    print()
    print("🎯 修复效果:")
    print("- 有序列表现在显示数字序号 (1. 2. 3.)")
    print("- 无序列表现在显示圆点符号 (•)")
    print("- 保持了原有的缩进和间距")
    print("- 不影响其他Markdown格式")
    print()
    print("🔧 技术细节:")
    print("- 修复文件: templates/index.html, templates/random.html")
    print("- 添加CSS: .markdown-content ol { list-style-type: decimal; }")
    print("- 添加CSS: .markdown-content ul { list-style-type: disc; }")
    print("- 覆盖了Tailwind CSS的重置样式")

if __name__ == '__main__':
    main()
