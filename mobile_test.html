<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>移动端对话框测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="static/css/tailwind-custom.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Roboto, "Helvetica Neue", Arial, sans-serif;
            background: #f8fafc;
            padding: 20px;
        }
        
        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .device-info {
            background: white;
            padding: 15px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="device-info">
        <h2>设备信息</h2>
        <p>屏幕宽度: <span id="screenWidth"></span>px</p>
        <p>屏幕高度: <span id="screenHeight"></span>px</p>
        <p>视口宽度: <span id="viewportWidth"></span>px</p>
        <p>视口高度: <span id="viewportHeight"></span>px</p>
        <p>设备像素比: <span id="devicePixelRatio"></span></p>
        <p>用户代理: <span id="userAgent"></span></p>
    </div>
    
    <button class="test-button" onclick="showEditModal()">测试编辑对话框</button>
    <button class="test-button" onclick="fillLongContent()">填充长内容测试</button>
    <button class="test-button" onclick="testKeyboard()">测试虚拟键盘</button>
    <button class="test-button" onclick="testPadding()">测试内边距优化</button>
    <button class="test-button" onclick="testTags()">测试标签区域</button>
    
    <!-- 包含编辑对话框 -->
    <!-- 编辑微博对话框 -->
    <div class="modal fixed z-50 left-0 top-0 w-full h-full bg-black/50 backdrop-blur-sm animate-fade-in" id="editModal" style="display: none;">
        <div class="bg-white my-[5%] mx-auto rounded-2xl w-[90%] max-w-2xl shadow-2xl animate-slide-in overflow-hidden max-h-[90vh] flex flex-col">
            <div class="flex justify-between items-center px-8 py-6 bg-gradient-to-br from-indigo-50/50 to-purple-50/50 border-b border-gray-200 flex-shrink-0">
                <div class="flex items-center gap-2">
                    <input type="datetime-local" id="editPostTime" name="editPostTime" form="editForm"
                           class="px-2 py-1 text-xs border-2 border-gray-200 rounded-xl transition-all duration-300 bg-white text-gray-800 font-inherit focus:outline-none focus:border-indigo-500 focus:ring-4 focus:ring-indigo-100">
                    <button type="button" id="setCurrentTimeBtn" class="px-2 py-1 text-xs font-semibold bg-indigo-600 text-white border-0 rounded-xl cursor-pointer transition-all duration-300 whitespace-nowrap shadow-sm hover:bg-indigo-700 hover:-translate-y-0.5 hover:shadow-md">Now</button>
                </div>
                <button type="button" class="bg-none border-0 text-2xl text-gray-500 cursor-pointer p-0 w-8 h-8 flex items-center justify-center rounded-full transition-all duration-300 hover:bg-red-50 hover:text-red-500" onclick="hideEditModal()">&times;</button>
            </div>
            <div class="p-8 flex-1 overflow-y-auto">
                <form id="editForm" class="space-y-6">
                    <div class="flex flex-col gap-2">
                        <!-- Markdown编辑器工具栏 -->
                        <div class="markdown-toolbar">
                            <button type="button" class="toolbar-btn" data-action="bold" title="粗体">
                                <strong>B</strong>
                            </button>
                            <button type="button" class="toolbar-btn" data-action="italic" title="斜体">
                                <em>I</em>
                            </button>
                            <button type="button" class="toolbar-btn" data-action="heading" title="标题">
                                H
                            </button>
                            <button type="button" class="toolbar-btn" data-action="link" title="链接">
                                🔗
                            </button>
                            <button type="button" class="toolbar-btn" data-action="code" title="代码">
                                &lt;/&gt;
                            </button>
                            <button type="button" class="toolbar-btn" data-action="list" title="列表">
                                ≡
                            </button>
                            <div class="toolbar-divider"></div>
                            <button type="button" class="toolbar-btn preview-btn" data-action="preview" title="预览">
                                👁
                            </button>
                        </div>

                        <!-- 编辑器容器 -->
                        <div class="editor-container">
                            <textarea name="content" id="editMarkdownEditor" placeholder="支持Markdown格式，编辑微博内容" required
                                      class="w-full min-h-250 md:min-h-300 p-6 text-base border-none font-mono resize-y bg-white focus:outline-none"></textarea>
                        </div>
                    </div>
                    <!-- 标签编辑区域 -->
                    <div class="flex items-center justify-between gap-4 md:flex-col md:items-stretch md:gap-4">
                        <div class="flex-1 min-w-0">
                            <label for="editTagInput" class="block text-sm font-medium text-gray-700 mb-1.5">
                                编辑标签 <span class="text-gray-400 text-xs">(输入标签名称后按 Enter 键添加，点击标签上的 × 可删除)</span>
                            </label>
                            <div class="group relative flex items-center flex-wrap gap-2 p-3 border-2 border-gray-200 rounded-xl bg-white min-h-[56px] transition-all duration-300 focus-within:border-indigo-500 focus-within:ring-4 focus-within:ring-indigo-100 focus-within:shadow-sm md:min-h-[50px] md:p-2.5">
                                <!-- 标签容器 -->
                                <div class="flex flex-wrap gap-2" id="editTagsContainer"></div>

                                <!-- 标签输入框 -->
                                <input type="text"
                                       id="editTagInput"
                                       placeholder="添加标签..."
                                       class="flex-1 min-w-[120px] border-0 outline-0 p-1 text-sm bg-transparent text-gray-700 placeholder-gray-400 md:min-w-[100px]" />

                                <!-- 输入提示图标 -->
                                <div class="flex-shrink-0 opacity-40 group-focus-within:opacity-60 transition-opacity duration-300">
                                    <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                    </svg>
                                </div>
                            </div>
                            <input type="hidden" name="tags" id="editTagsField" value="[]" />
                        </div>
                    </div>
                </form>
            </div>
            <div class="px-8 py-6 bg-gray-50 border-t border-gray-200 flex justify-end gap-4 flex-shrink-0">
                <button type="submit" form="editForm" class="px-6 py-2 text-sm font-semibold bg-gradient-to-r from-indigo-500 to-purple-600 text-white border-0 rounded-xl cursor-pointer transition-all duration-300 shadow-sm hover:-translate-y-1 hover:shadow-md">保存修改</button>
            </div>
        </div>
    </div>

    <script>
        // 显示设备信息
        function updateDeviceInfo() {
            document.getElementById('screenWidth').textContent = screen.width;
            document.getElementById('screenHeight').textContent = screen.height;
            document.getElementById('viewportWidth').textContent = window.innerWidth;
            document.getElementById('viewportHeight').textContent = window.innerHeight;
            document.getElementById('devicePixelRatio').textContent = window.devicePixelRatio;
            document.getElementById('userAgent').textContent = navigator.userAgent;
        }
        
        // 显示编辑对话框
        function showEditModal() {
            document.getElementById('editModal').style.display = 'block';
            document.body.style.overflow = 'hidden';
        }
        
        // 隐藏编辑对话框
        function hideEditModal() {
            document.getElementById('editModal').style.display = 'none';
            document.body.style.overflow = '';
        }
        
        // 填充长内容测试
        function fillLongContent() {
            const textarea = document.getElementById('editMarkdownEditor');
            const longContent = `# 这是一个很长的测试内容

## 第一部分
这是第一部分的内容，用来测试对话框在移动端的显示效果。我们需要确保即使内容很长，用户也能够正常滚动查看所有内容。

## 第二部分
这是第二部分的内容。在移动设备上，我们特别需要注意以下几点：
1. 对话框的高度不应该超过屏幕高度
2. 内容应该可以正常滚动
3. 保存按钮应该始终可见
4. 虚拟键盘弹出时不应该遮挡重要内容

## 第三部分
这是第三部分的内容，继续测试长内容的显示效果。

## 第四部分
更多的测试内容，确保滚动功能正常工作。

## 第五部分
最后一部分的测试内容，验证所有功能都能正常使用。`;
            
            textarea.value = longContent;
            showEditModal();
        }
        
        // 测试虚拟键盘
        function testKeyboard() {
            showEditModal();
            setTimeout(() => {
                document.getElementById('editTagInput').focus();
            }, 500);
        }

        // 测试内边距优化
        function testPadding() {
            const textarea = document.getElementById('editMarkdownEditor');
            textarea.value = `# 内边距优化测试

这是一个测试内容，用来验证移动端内边距优化的效果。

## 测试要点
1. 内容区域的内边距是否合适
2. 标签区域的内边距是否紧凑
3. 整体布局是否更加节省空间

请在不同屏幕尺寸下测试这个对话框的显示效果。`;

            showEditModal();
        }

        // 测试标签区域
        function testTags() {
            showEditModal();
            setTimeout(() => {
                // 模拟添加一些标签
                const tagInput = document.getElementById('editTagInput');
                const tagContainer = document.getElementById('editTagsContainer');

                // 添加测试标签
                const testTags = ['移动端', '内边距', '优化', '测试'];
                testTags.forEach(tag => {
                    const tagElement = document.createElement('span');
                    tagElement.className = 'tag';
                    tagElement.textContent = tag;
                    tagContainer.appendChild(tagElement);
                });

                tagInput.focus();
            }, 500);
        }
        
        // 初始化
        updateDeviceInfo();
        
        // 监听窗口大小变化
        window.addEventListener('resize', updateDeviceInfo);
        
        // 点击背景关闭对话框
        document.getElementById('editModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideEditModal();
            }
        });
    </script>
</body>
</html>
