#!/usr/bin/env python3
"""
创建测试用户
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from models import db, User
from app import app

def create_test_user():
    """创建测试用户"""
    with app.app_context():
        # 检查是否已存在测试用户
        existing_user = User.query.filter_by(username='test').first()
        if existing_user:
            print("测试用户已存在")
            return
        
        # 创建测试用户
        test_user = User(username='test', password='test')
        db.session.add(test_user)
        db.session.commit()
        print("测试用户创建成功: username=test, password=test")

if __name__ == '__main__':
    create_test_user()
